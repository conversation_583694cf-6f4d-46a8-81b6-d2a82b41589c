#!/usr/bin/env python3
"""
Deep Risk RAG Worker Service 验证脚本
用于验证和测试Celery任务队列的功能和性能

功能特性:
- 健康检查验证
- 向量化任务验证 
- 风险分析任务验证
- 批量任务测试
- 实时监控和诊断
- 详细报告生成

作者: AI Assistant
日期: 2024-12-19
"""

import os
import sys
import time
import json
import asyncio
import tempfile
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from contextlib import asynccontextmanager

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.layout import Layout
    from rich.live import Live
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.prompt import Prompt, Confirm
    from rich.text import Text
    from rich.tree import Tree
    from rich.status import Status
    from rich import box
except ImportError:
    print("❌ Rich库未安装，请运行: pip install rich")
    sys.exit(1)

try:
    import psutil
except ImportError:
    print("❌ psutil库未安装，请运行: pip install psutil")
    sys.exit(1)

try:
    import redis
    from celery import Celery
    from celery.result import AsyncResult
except ImportError:
    print("❌ Redis/Celery库未安装，请运行: pip install redis celery")
    sys.exit(1)

# 导入项目模块
from shared.utils.logger import get_logger
from shared.redis_config import redis_config, test_redis_connection
from shared.celery_config import celery_app, TaskNames
from shared.models.file_info import FileInfo, FileStatus
from services.worker_service.config import config as worker_config

logger = get_logger(__name__)


class WorkerValidator:
    """Worker服务验证器"""
    
    def __init__(self):
        self.console = Console()
        self.results = {
            'connection_tests': {},
            'task_tests': {},
            'performance_tests': {},
            'errors': []
        }
        self.start_time = datetime.now()
        self.test_files = []
    
    def print_header(self):
        """打印验证脚本头部信息"""
        self.console.clear()
        
        header_text = """
🚀 Deep Risk RAG Worker Service 验证工具
═══════════════════════════════════════════════════════════════
        """
        
        info_table = Table(show_header=False, box=box.ROUNDED)
        info_table.add_column("项目", style="cyan bold")
        info_table.add_column("值", style="white")
        
        info_table.add_row("🌐 Redis地址", f"{redis_config.host}:{redis_config.port}")
        info_table.add_row("🗄️ ChromaDB", worker_config.chroma_url)
        info_table.add_row("🤖 LLM提供商", worker_config.llm_provider)
        info_table.add_row("📦 模型路径", worker_config.model_name)
        info_table.add_row("🔧 设备", worker_config.device)
        info_table.add_row("⏰ 开始时间", self.start_time.strftime("%Y-%m-%d %H:%M:%S"))
        
        self.console.print(Panel(header_text, style="bold green"))
        self.console.print(info_table)
        self.console.print()
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            memory = psutil.virtual_memory()
            disk = psutil.disk_usage('/')
            
            return {
                'cpu_percent': cpu_percent,
                'memory_total': memory.total,
                'memory_used': memory.used,
                'memory_percent': memory.percent,
                'disk_total': disk.total,
                'disk_used': disk.used,
                'disk_percent': (disk.used / disk.total) * 100,
                'python_version': sys.version.split()[0],
                'platform': sys.platform
            }
        except Exception as e:
            logger.error(f"获取系统信息失败: {e}")
            return {}
    
    def test_redis_connection(self) -> Tuple[bool, str]:
        """测试Redis连接"""
        try:
            client = redis_config.client
            client.ping()
            info = client.info()
            
            return True, f"连接成功 - 版本: {info.get('redis_version', 'unknown')}"
        except Exception as e:
            return False, f"连接失败: {str(e)}"
    
    def test_chroma_connection(self) -> Tuple[bool, str]:
        """测试ChromaDB连接"""
        try:
            import requests
            response = requests.get(f"{worker_config.chroma_url}/api/v1/heartbeat", timeout=5)
            if response.status_code == 200:
                return True, "连接成功"
            else:
                return False, f"HTTP {response.status_code}"
        except Exception as e:
            return False, f"连接失败: {str(e)}"
    
    def test_celery_connection(self) -> Tuple[bool, str]:
        """测试Celery连接"""
        try:
            # 检查Celery实例
            inspect = celery_app.control.inspect()
            stats = inspect.stats()
            
            if stats:
                worker_count = len(stats)
                return True, f"发现 {worker_count} 个活跃Worker"
            else:
                return False, "未发现活跃的Worker"
        except Exception as e:
            return False, f"连接失败: {str(e)}"
    
    def create_test_file(self, content: str, filename: str) -> str:
        """创建测试文件"""
        temp_dir = Path(tempfile.gettempdir()) / "worker_validation"
        temp_dir.mkdir(exist_ok=True)
        
        file_path = temp_dir / filename
        file_path.write_text(content, encoding='utf-8')
        
        self.test_files.append(file_path)
        return str(file_path)
    
    async def test_health_check_task(self) -> Tuple[bool, str, Dict]:
        """测试健康检查任务"""
        try:
            # 提交健康检查任务
            result = celery_app.send_task('worker.tasks.health_check')
            
            # 等待结果，最多30秒
            start_time = time.time()
            while not result.ready() and time.time() - start_time < 30:
                await asyncio.sleep(1)
            
            if result.ready():
                if result.successful():
                    task_result = result.get()
                    return True, "健康检查通过", task_result
                else:
                    return False, f"健康检查失败: {result.result}", {}
            else:
                return False, "任务超时（30秒）", {}
                
        except Exception as e:
            return False, f"健康检查异常: {str(e)}", {}
    
    async def test_vectorize_task(self) -> Tuple[bool, str, Dict]:
        """测试向量化任务"""
        try:
            # 创建测试文件
            test_content = """
            这是一个用于测试的文档。
            内容包含了一些中文和英文文本。
            This document is used for testing purposes.
            It contains both Chinese and English text.
            """
            
            file_path = self.create_test_file(test_content, "test_vectorize.txt")
            file_code = f"test_{int(time.time())}"
            
            # 创建文件信息
            file_info = FileInfo(
                file_code=file_code,
                original_name="test_vectorize.txt",
                file_path=file_path,
                file_size=len(test_content.encode('utf-8')),
                file_type="text/plain",
                status=FileStatus.UPLOADED
            )
            
            # 提交向量化任务
            result = celery_app.send_task(
                'worker.tasks.vectorize_file',
                args=[file_code, file_path, file_info.dict()]
            )
            
            # 等待结果，最多60秒
            start_time = time.time()
            while not result.ready() and time.time() - start_time < 60:
                await asyncio.sleep(2)
            
            if result.ready():
                if result.successful():
                    task_result = result.get()
                    return True, f"向量化成功，处理了 {task_result.get('document_count', 0)} 个文档块", task_result
                else:
                    return False, f"向量化失败: {result.result}", {}
            else:
                return False, "向量化任务超时（60秒）", {}
                
        except Exception as e:
            return False, f"向量化任务异常: {str(e)}", {}
    
    async def test_analysis_task(self) -> Tuple[bool, str, Dict]:
        """测试风险分析任务"""
        try:
            # 使用之前向量化的文件进行分析
            file_code = f"test_{int(time.time() - 100)}"  # 使用较早的文件代码
            
            # 提交分析任务
            result = celery_app.send_task(
                'worker.tasks.analyze_risk',
                args=[file_code, "default"]
            )
            
            # 等待结果，最多120秒
            start_time = time.time()
            while not result.ready() and time.time() - start_time < 120:
                await asyncio.sleep(3)
            
            if result.ready():
                if result.successful():
                    task_result = result.get()
                    return True, "风险分析完成", task_result
                else:
                    return False, f"风险分析失败: {result.result}", {}
            else:
                return False, "风险分析任务超时（120秒）", {}
                
        except Exception as e:
            return False, f"风险分析任务异常: {str(e)}", {}
    
    def run_connection_tests(self) -> Dict[str, Tuple[bool, str]]:
        """运行连接测试"""
        tests = {
            'Redis': self.test_redis_connection,
            'ChromaDB': self.test_chroma_connection,
            'Celery': self.test_celery_connection
        }
        
        results = {}
        with Progress() as progress:
            task = progress.add_task("连接测试", total=len(tests))
            
            for name, test_func in tests.items():
                progress.update(task, description=f"测试 {name} 连接...")
                results[name] = test_func()
                progress.advance(task)
        
        return results
    
    async def run_task_tests(self) -> Dict[str, Tuple[bool, str, Dict]]:
        """运行任务测试"""
        tests = {
            '健康检查': self.test_health_check_task,
            '向量化': self.test_vectorize_task,
            '风险分析': self.test_analysis_task
        }
        
        results = {}
        
        with Progress() as progress:
            task = progress.add_task("任务测试", total=len(tests))
            
            for name, test_func in tests.items():
                progress.update(task, description=f"测试 {name} 任务...")
                results[name] = await test_func()
                progress.advance(task)
        
        return results
    
    def display_results(self, connection_results: Dict, task_results: Dict):
        """显示测试结果"""
        self.console.print("\n" + "="*80)
        self.console.print("[bold green]🎯 验证结果总结[/bold green]")
        self.console.print("="*80)
        
        # 连接测试结果
        conn_table = Table(title="📡 连接测试结果", box=box.ROUNDED)
        conn_table.add_column("服务", style="cyan")
        conn_table.add_column("状态", justify="center")
        conn_table.add_column("详情", style="white")
        
        for service, (success, message) in connection_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            conn_table.add_row(service, status, message)
        
        self.console.print(conn_table)
        self.console.print()
        
        # 任务测试结果
        task_table = Table(title="⚙️ 任务测试结果", box=box.ROUNDED)
        task_table.add_column("任务类型", style="cyan")
        task_table.add_column("状态", justify="center")
        task_table.add_column("结果", style="white")
        task_table.add_column("性能", style="yellow")
        
        for task_name, (success, message, details) in task_results.items():
            status = "✅ 成功" if success else "❌ 失败"
            perf_info = ""
            if details and 'processing_time' in details:
                perf_info = f"{details['processing_time']:.2f}s"
            elif details and 'total_time' in details:
                perf_info = f"{details['total_time']:.2f}s"
            
            task_table.add_row(task_name, status, message, perf_info)
        
        self.console.print(task_table)
        
        # 系统信息
        sys_info = self.get_system_info()
        if sys_info:
            self.console.print()
            sys_table = Table(title="💻 系统资源状态", box=box.ROUNDED)
            sys_table.add_column("资源", style="cyan")
            sys_table.add_column("使用情况", style="white")
            
            sys_table.add_row("CPU", f"{sys_info.get('cpu_percent', 0):.1f}%")
            sys_table.add_row("内存", f"{sys_info.get('memory_percent', 0):.1f}%")
            sys_table.add_row("磁盘", f"{sys_info.get('disk_percent', 0):.1f}%")
            
            self.console.print(sys_table)
    
    def cleanup(self):
        """清理测试文件"""
        for file_path in self.test_files:
            try:
                if file_path.exists():
                    file_path.unlink()
            except Exception as e:
                logger.warning(f"清理测试文件失败 {file_path}: {e}")
    
    async def run_full_validation(self):
        """运行完整验证"""
        try:
            self.print_header()
            
            # 1. 连接测试
            self.console.print("[bold blue]🔍 开始连接测试...[/bold blue]")
            connection_results = self.run_connection_tests()
            self.results['connection_tests'] = connection_results
            
            # 检查关键连接
            redis_ok = connection_results.get('Redis', (False, ''))[0]
            celery_ok = connection_results.get('Celery', (False, ''))[0]
            
            if not redis_ok:
                self.console.print("[red]❌ Redis连接失败，无法继续任务测试[/red]")
                return
            
            if not celery_ok:
                self.console.print("[yellow]⚠️ 未发现活跃Worker，部分任务测试可能失败[/yellow]")
            
            # 2. 任务测试
            self.console.print("\n[bold blue]⚙️ 开始任务测试...[/bold blue]")
            task_results = await self.run_task_tests()
            self.results['task_tests'] = task_results
            
            # 3. 显示结果
            self.display_results(connection_results, task_results)
            
            # 4. 总结
            total_time = (datetime.now() - self.start_time).total_seconds()
            
            # 计算成功率
            conn_success = sum(1 for success, _ in connection_results.values() if success)
            task_success = sum(1 for success, _, _ in task_results.values() if success)
            
            total_tests = len(connection_results) + len(task_results)
            total_success = conn_success + task_success
            success_rate = (total_success / total_tests) * 100 if total_tests > 0 else 0
            
            self.console.print(f"\n[bold]📊 验证完成 - 总耗时: {total_time:.2f}秒[/bold]")
            self.console.print(f"[bold]📈 成功率: {success_rate:.1f}% ({total_success}/{total_tests})[/bold]")
            
            if success_rate >= 80:
                self.console.print("[bold green]🎉 Worker服务运行状况良好![/bold green]")
            elif success_rate >= 60:
                self.console.print("[bold yellow]⚠️ Worker服务存在一些问题，建议检查[/bold yellow]")
            else:
                self.console.print("[bold red]❌ Worker服务存在严重问题，需要立即处理[/bold red]")
        
        finally:
            self.cleanup()


def main():
    """主函数"""
    try:
        # 检查运行环境
        if not Path(project_root / "services" / "worker_service").exists():
            print("❌ 请在项目根目录运行此脚本")
            sys.exit(1)
        
        validator = WorkerValidator()
        
        # 显示菜单
        validator.console.print("[bold cyan]选择验证模式:[/bold cyan]")
        validator.console.print("1. 完整验证 (推荐)")
        validator.console.print("2. 仅连接测试")
        validator.console.print("3. 仅任务测试")
        validator.console.print("4. 退出")
        
        choice = Prompt.ask("请选择", choices=["1", "2", "3", "4"], default="1")
        
        if choice == "4":
            validator.console.print("👋 退出验证工具")
            return
        
        # 运行验证
        if choice == "1":
            asyncio.run(validator.run_full_validation())
        elif choice == "2":
            validator.print_header()
            connection_results = validator.run_connection_tests()
            validator.display_results(connection_results, {})
        elif choice == "3":
            validator.print_header()
            validator.console.print("[yellow]⚠️ 跳过连接测试，直接运行任务测试[/yellow]")
            task_results = asyncio.run(validator.run_task_tests())
            validator.display_results({}, task_results)
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出验证工具")
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        logger.exception("验证工具异常")


if __name__ == "__main__":
    main() 