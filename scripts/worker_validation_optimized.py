#!/usr/bin/env python3
"""
Deep Risk RAG Worker Service 验证脚本 (优化版)
修复了异步调用、错误处理、资源管理等问题

优化内容:
- 改进异步函数设计和调用
- 增强错误分类和处理
- 优化资源管理和清理
- 增加配置灵活性
- 提升性能和稳定性

作者: AI Assistant  
日期: 2024-12-19
版本: 2.0 (优化版)
"""

import os
import sys
import time
import json
import asyncio
import tempfile
import traceback
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple, NamedTuple
from contextlib import asynccontextmanager
from dataclasses import dataclass
from enum import Enum

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

try:
    from rich.console import Console
    from rich.table import Table
    from rich.panel import Panel
    from rich.progress import Progress, SpinnerColumn, TextColumn, BarColumn, TimeElapsedColumn
    from rich.prompt import Prompt, Confirm
    from rich.status import Status
    from rich import box
    HAS_RICH = True
except ImportError:
    HAS_RICH = False

try:
    import psutil
    HAS_PSUTIL = True
except ImportError:
    HAS_PSUTIL = False

try:
    import redis
    from celery import Celery
    from celery.result import AsyncResult
    HAS_CELERY = True
except ImportError:
    HAS_CELERY = False

# 导入项目模块
try:
    from shared.utils.logger import get_logger
    from shared.redis_config import redis_config
    from shared.celery_config import celery_app
    from shared.models.file_info import FileInfo, FileStatus
    from services.worker_service.config import config as worker_config
    HAS_PROJECT_MODULES = True
except ImportError as e:
    HAS_PROJECT_MODULES = False
    print(f"⚠️ 项目模块导入失败: {e}")


class TestResult(NamedTuple):
    """测试结果结构"""
    success: bool
    message: str
    details: Dict[str, Any]
    duration: float
    error_code: Optional[str] = None


class ErrorCode(Enum):
    """错误代码枚举"""
    CONNECTION_REFUSED = "CONN_REFUSED"
    TIMEOUT = "TIMEOUT"
    AUTH_FAILED = "AUTH_FAILED"
    SERVICE_UNAVAILABLE = "SERVICE_UNAVAIL"
    TASK_FAILED = "TASK_FAILED"
    IMPORT_ERROR = "IMPORT_ERROR"
    CONFIG_ERROR = "CONFIG_ERROR"


@dataclass
class ValidationConfig:
    """验证配置类"""
    health_check_timeout: int = 30
    vectorize_timeout: int = 60
    analysis_timeout: int = 120
    max_retries: int = 3
    retry_delay: float = 2.0
    cleanup_on_exit: bool = True
    verbose: bool = True
    
    # 连接配置
    redis_connection_timeout: float = 5.0
    chroma_connection_timeout: float = 5.0
    celery_stats_timeout: float = 10.0


class OptimizedWorkerValidator:
    """优化的Worker服务验证器"""
    
    def __init__(self, config: Optional[ValidationConfig] = None):
        self.config = config or ValidationConfig()
        self.console = Console() if HAS_RICH else None
        self.logger = get_logger(__name__) if HAS_PROJECT_MODULES else None
        
        self.results = {
            'connection_tests': {},
            'task_tests': {},
            'performance_tests': {},
            'errors': []
        }
        self.start_time = datetime.now()
        self.test_files = []
        self._redis_client = None
        self._cleanup_tasks = []
    
    def _log(self, message: str, level: str = "info"):
        """统一的日志记录"""
        if self.logger:
            getattr(self.logger, level)(message)
        if self.config.verbose:
            timestamp = datetime.now().strftime("%H:%M:%S")
            print(f"[{timestamp}] {message}")
    
    def _print_rich(self, content, **kwargs):
        """Rich输出的包装"""
        if self.console:
            self.console.print(content, **kwargs)
        else:
            print(str(content))
    
    async def _retry_async(self, func, *args, **kwargs):
        """异步重试装饰器"""
        last_exception = None
        
        for attempt in range(self.config.max_retries):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                if attempt < self.config.max_retries - 1:
                    self._log(f"重试 {attempt + 1}/{self.config.max_retries}: {e}", "warning")
                    await asyncio.sleep(self.config.retry_delay)
                else:
                    self._log(f"重试失败，达到最大重试次数: {e}", "error")
        
        raise last_exception
    
    def _get_error_code(self, exception: Exception) -> ErrorCode:
        """根据异常类型返回错误代码"""
        error_msg = str(exception).lower()
        
        if "connection refused" in error_msg or "connect" in error_msg:
            return ErrorCode.CONNECTION_REFUSED
        elif "timeout" in error_msg:
            return ErrorCode.TIMEOUT
        elif "auth" in error_msg or "permission" in error_msg:
            return ErrorCode.AUTH_FAILED
        elif "unavailable" in error_msg or "not found" in error_msg:
            return ErrorCode.SERVICE_UNAVAILABLE
        elif "import" in error_msg:
            return ErrorCode.IMPORT_ERROR
        else:
            return ErrorCode.TASK_FAILED
    
    def print_header(self):
        """打印验证脚本头部信息"""
        if self.console:
            self.console.clear()
        
        header_text = """
🚀 Deep Risk RAG Worker Service 验证工具 (优化版)
═══════════════════════════════════════════════════════════════
        """
        
        if self.console:
            info_table = Table(show_header=False, box=box.ROUNDED)
            info_table.add_column("项目", style="cyan bold")
            info_table.add_column("值", style="white")
            
            if HAS_PROJECT_MODULES:
                info_table.add_row("🌐 Redis地址", f"{redis_config.host}:{redis_config.port}")
                info_table.add_row("🗄️ ChromaDB", worker_config.chroma_url)
                info_table.add_row("🤖 LLM提供商", worker_config.llm_provider)
                info_table.add_row("📦 模型路径", worker_config.model_name)
                info_table.add_row("🔧 设备", worker_config.device)
            
            info_table.add_row("⏰ 开始时间", self.start_time.strftime("%Y-%m-%d %H:%M:%S"))
            info_table.add_row("📋 配置", f"超时: {self.config.vectorize_timeout}s, 重试: {self.config.max_retries}")
            
            self._print_rich(Panel(header_text, style="bold green"))
            self._print_rich(info_table)
        else:
            print(header_text)
            if HAS_PROJECT_MODULES:
                print(f"🌐 Redis地址: {redis_config.host}:{redis_config.port}")
                print(f"🗄️ ChromaDB: {worker_config.chroma_url}")
                print(f"🤖 LLM提供商: {worker_config.llm_provider}")
            print(f"⏰ 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print()
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            'python_version': sys.version.split()[0],
            'platform': sys.platform,
            'has_rich': HAS_RICH,
            'has_psutil': HAS_PSUTIL,
            'has_celery': HAS_CELERY,
            'has_project_modules': HAS_PROJECT_MODULES
        }
        
        if HAS_PSUTIL:
            try:
                cpu_percent = psutil.cpu_percent(interval=1)
                memory = psutil.virtual_memory()
                disk = psutil.disk_usage('/')
                
                info.update({
                    'cpu_percent': cpu_percent,
                    'memory_total': memory.total,
                    'memory_used': memory.used,
                    'memory_percent': memory.percent,
                    'disk_total': disk.total,
                    'disk_used': disk.used,
                    'disk_percent': (disk.used / disk.total) * 100,
                })
            except Exception as e:
                self._log(f"获取系统信息失败: {e}", "warning")
        
        return info
    
    async def test_redis_connection(self) -> TestResult:
        """测试Redis连接"""
        start_time = time.time()
        
        if not HAS_PROJECT_MODULES:
            return TestResult(
                False, "项目模块未正确导入", {},
                time.time() - start_time, ErrorCode.IMPORT_ERROR.value
            )
        
        try:
            # 使用连接池获取客户端
            if not self._redis_client:
                self._redis_client = redis_config.client
            
            # 测试基本连接
            await asyncio.wait_for(
                asyncio.to_thread(self._redis_client.ping),
                timeout=self.config.redis_connection_timeout
            )
            
            # 获取服务器信息
            info = await asyncio.wait_for(
                asyncio.to_thread(self._redis_client.info),
                timeout=self.config.redis_connection_timeout
            )
            
            details = {
                'redis_version': info.get('redis_version', 'unknown'),
                'used_memory': info.get('used_memory_human', 'unknown'),
                'connected_clients': info.get('connected_clients', 0),
                'uptime_in_seconds': info.get('uptime_in_seconds', 0)
            }
            
            return TestResult(
                True,
                f"连接成功 - 版本: {details['redis_version']}",
                details,
                time.time() - start_time
            )
            
        except asyncio.TimeoutError:
            return TestResult(
                False, f"连接超时 ({self.config.redis_connection_timeout}s)", {},
                time.time() - start_time, ErrorCode.TIMEOUT.value
            )
        except Exception as e:
            error_code = self._get_error_code(e)
            return TestResult(
                False, f"连接失败: {str(e)}", {},
                time.time() - start_time, error_code.value
            )
    
    async def test_chroma_connection(self) -> TestResult:
        """测试ChromaDB连接"""
        start_time = time.time()
        
        if not HAS_PROJECT_MODULES:
            return TestResult(
                False, "项目模块未正确导入", {},
                time.time() - start_time, ErrorCode.IMPORT_ERROR.value
            )
        
        try:
            import aiohttp
            
            async with aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.chroma_connection_timeout)
            ) as session:
                url = f"{worker_config.chroma_url}/api/v1/heartbeat"
                async with session.get(url) as response:
                    if response.status == 200:
                        # 尝试获取版本信息
                        try:
                            version_url = f"{worker_config.chroma_url}/api/v1/version"
                            async with session.get(version_url) as version_resp:
                                if version_resp.status == 200:
                                    version_data = await version_resp.json()
                                    details = {'version': version_data.get('version', 'unknown')}
                                else:
                                    details = {}
                        except:
                            details = {}
                        
                        return TestResult(
                            True, "连接成功", details,
                            time.time() - start_time
                        )
                    else:
                        return TestResult(
                            False, f"HTTP {response.status}", {},
                            time.time() - start_time, ErrorCode.SERVICE_UNAVAILABLE.value
                        )
                        
        except asyncio.TimeoutError:
            return TestResult(
                False, f"连接超时 ({self.config.chroma_connection_timeout}s)", {},
                time.time() - start_time, ErrorCode.TIMEOUT.value
            )
        except ImportError:
            # 回退到同步请求
            try:
                import requests
                response = requests.get(
                    f"{worker_config.chroma_url}/api/v1/heartbeat",
                    timeout=self.config.chroma_connection_timeout
                )
                if response.status_code == 200:
                    return TestResult(
                        True, "连接成功", {},
                        time.time() - start_time
                    )
                else:
                    return TestResult(
                        False, f"HTTP {response.status_code}", {},
                        time.time() - start_time, ErrorCode.SERVICE_UNAVAILABLE.value
                    )
            except Exception as e:
                error_code = self._get_error_code(e)
                return TestResult(
                    False, f"连接失败: {str(e)}", {},
                    time.time() - start_time, error_code.value
                )
        except Exception as e:
            error_code = self._get_error_code(e)
            return TestResult(
                False, f"连接失败: {str(e)}", {},
                time.time() - start_time, error_code.value
            )
    
    async def test_celery_connection(self) -> TestResult:
        """测试Celery连接"""
        start_time = time.time()
        
        if not HAS_PROJECT_MODULES or not HAS_CELERY:
            return TestResult(
                False, "依赖库未正确导入", {},
                time.time() - start_time, ErrorCode.IMPORT_ERROR.value
            )
        
        try:
            # 异步获取Worker统计信息
            inspect = celery_app.control.inspect()
            
            stats = await asyncio.wait_for(
                asyncio.to_thread(inspect.stats),
                timeout=self.config.celery_stats_timeout
            )
            
            if stats:
                worker_count = len(stats)
                
                # 获取活跃队列信息
                try:
                    active_queues = await asyncio.wait_for(
                        asyncio.to_thread(inspect.active_queues),
                        timeout=5.0
                    )
                    queue_count = sum(len(queues) for queues in active_queues.values()) if active_queues else 0
                except:
                    queue_count = 0
                
                details = {
                    'worker_count': worker_count,
                    'active_queues': queue_count,
                    'worker_nodes': list(stats.keys())
                }
                
                return TestResult(
                    True,
                    f"发现 {worker_count} 个活跃Worker",
                    details,
                    time.time() - start_time
                )
            else:
                return TestResult(
                    False, "未发现活跃的Worker", {},
                    time.time() - start_time, ErrorCode.SERVICE_UNAVAILABLE.value
                )
                
        except asyncio.TimeoutError:
            return TestResult(
                False, f"连接超时 ({self.config.celery_stats_timeout}s)", {},
                time.time() - start_time, ErrorCode.TIMEOUT.value
            )
        except Exception as e:
            error_code = self._get_error_code(e)
            return TestResult(
                False, f"连接失败: {str(e)}", {},
                time.time() - start_time, error_code.value
            )
    
    @asynccontextmanager
    async def _create_test_file(self, content: str, filename: str):
        """异步上下文管理器创建测试文件"""
        temp_dir = Path(tempfile.gettempdir()) / "worker_validation"
        temp_dir.mkdir(exist_ok=True)
        
        file_path = temp_dir / filename
        try:
            await asyncio.to_thread(file_path.write_text, content, encoding='utf-8')
            self.test_files.append(file_path)
            yield str(file_path)
        finally:
            if self.config.cleanup_on_exit and file_path.exists():
                try:
                    await asyncio.to_thread(file_path.unlink)
                    if file_path in self.test_files:
                        self.test_files.remove(file_path)
                except Exception as e:
                    self._log(f"清理测试文件失败 {file_path}: {e}", "warning")
    
    async def test_health_check_task(self) -> TestResult:
        """测试健康检查任务"""
        start_time = time.time()
        
        if not HAS_PROJECT_MODULES or not HAS_CELERY:
            return TestResult(
                False, "依赖库未正确导入", {},
                time.time() - start_time, ErrorCode.IMPORT_ERROR.value
            )
        
        try:
            # 提交健康检查任务
            result = celery_app.send_task('worker.tasks.health_check')
            
            # 等待结果
            task_result = await asyncio.wait_for(
                asyncio.to_thread(result.get),
                timeout=self.config.health_check_timeout
            )
            
            details = {
                'task_id': result.id,
                'result': task_result,
                'worker_healthy': task_result.get('overall_healthy', False) if isinstance(task_result, dict) else False
            }
            
            success = task_result.get('overall_healthy', False) if isinstance(task_result, dict) else True
            message = "健康检查通过" if success else "健康检查发现问题"
            
            return TestResult(
                success, message, details,
                time.time() - start_time
            )
            
        except asyncio.TimeoutError:
            return TestResult(
                False, f"任务超时 ({self.config.health_check_timeout}s)", {},
                time.time() - start_time, ErrorCode.TIMEOUT.value
            )
        except Exception as e:
            error_code = self._get_error_code(e)
            return TestResult(
                False, f"健康检查异常: {str(e)}", {},
                time.time() - start_time, error_code.value
            )
    
    async def test_vectorize_task(self) -> TestResult:
        """测试向量化任务"""
        start_time = time.time()
        
        if not HAS_PROJECT_MODULES or not HAS_CELERY:
            return TestResult(
                False, "依赖库未正确导入", {},
                time.time() - start_time, ErrorCode.IMPORT_ERROR.value
            )
        
        try:
            # 创建测试文件
            test_content = """
            这是一个用于测试的文档。
            内容包含了一些中文和英文文本。
            This document is used for testing purposes.
            It contains both Chinese and English text.
            
            测试内容包括多种文本格式和语言混合的情况，
            用于验证BGE-M3模型的向量化能力。
            """
            
            async with self._create_test_file(test_content, "test_vectorize.txt") as file_path:
                file_code = f"test_{int(time.time())}"
                
                # 创建文件信息
                file_info = FileInfo(
                    file_code=file_code,
                    original_name="test_vectorize.txt",
                    file_path=file_path,
                    file_size=len(test_content.encode('utf-8')),
                    file_type="text/plain",
                    status=FileStatus.UPLOADED
                )
                
                # 提交向量化任务
                result = celery_app.send_task(
                    'worker.tasks.vectorize_file',
                    args=[file_code, file_path, file_info.dict()]
                )
                
                # 等待结果
                task_result = await asyncio.wait_for(
                    asyncio.to_thread(result.get),
                    timeout=self.config.vectorize_timeout
                )
                
                details = {
                    'task_id': result.id,
                    'file_code': file_code,
                    'document_count': task_result.get('document_count', 0),
                    'processing_time': task_result.get('processing_time', 0),
                    'parse_time': task_result.get('parse_time', 0),
                    'vector_time': task_result.get('vector_time', 0)
                }
                
                return TestResult(
                    True,
                    f"向量化成功，处理了 {details['document_count']} 个文档块",
                    details,
                    time.time() - start_time
                )
                
        except asyncio.TimeoutError:
            return TestResult(
                False, f"向量化任务超时 ({self.config.vectorize_timeout}s)", {},
                time.time() - start_time, ErrorCode.TIMEOUT.value
            )
        except Exception as e:
            error_code = self._get_error_code(e)
            return TestResult(
                False, f"向量化任务异常: {str(e)}", {},
                time.time() - start_time, error_code.value
            )
    
    async def test_analysis_task(self) -> TestResult:
        """测试风险分析任务"""
        start_time = time.time()
        
        if not HAS_PROJECT_MODULES or not HAS_CELERY:
            return TestResult(
                False, "依赖库未正确导入", {},
                time.time() - start_time, ErrorCode.IMPORT_ERROR.value
            )
        
        try:
            # 使用之前向量化的文件进行分析
            file_code = f"test_{int(time.time() - 100)}"
            
            # 提交分析任务
            result = celery_app.send_task(
                'worker.tasks.analyze_risk',
                args=[file_code, "default"]
            )
            
            # 等待结果
            task_result = await asyncio.wait_for(
                asyncio.to_thread(result.get),
                timeout=self.config.analysis_timeout
            )
            
            details = {
                'task_id': result.id,
                'file_code': file_code,
                'analysis_type': 'default',
                'processing_time': task_result.get('processing_time', 0),
                'analysis_time': task_result.get('analysis_time', 0)
            }
            
            # 检查分析结果
            if isinstance(task_result, dict) and task_result.get('success'):
                analysis_result = task_result.get('analysis_result', {})
                if isinstance(analysis_result, dict):
                    details['risk_level'] = analysis_result.get('risk_level', 'unknown')
                    details['confidence'] = analysis_result.get('confidence', 0)
                
                return TestResult(
                    True, "风险分析完成", details,
                    time.time() - start_time
                )
            else:
                return TestResult(
                    False, "风险分析失败", details,
                    time.time() - start_time, ErrorCode.TASK_FAILED.value
                )
                
        except asyncio.TimeoutError:
            return TestResult(
                False, f"风险分析任务超时 ({self.config.analysis_timeout}s)", {},
                time.time() - start_time, ErrorCode.TIMEOUT.value
            )
        except Exception as e:
            error_code = self._get_error_code(e)
            return TestResult(
                False, f"风险分析任务异常: {str(e)}", {},
                time.time() - start_time, error_code.value
            )
    
    async def run_connection_tests(self) -> Dict[str, TestResult]:
        """运行连接测试"""
        tests = {
            'Redis': self.test_redis_connection,
            'ChromaDB': self.test_chroma_connection,
            'Celery': self.test_celery_connection
        }
        
        results = {}
        
        if self.console:
            with Progress() as progress:
                task = progress.add_task("连接测试", total=len(tests))
                
                for name, test_func in tests.items():
                    progress.update(task, description=f"测试 {name} 连接...")
                    results[name] = await self._retry_async(test_func)
                    progress.advance(task)
        else:
            self._log("🔍 开始连接测试...")
            for name, test_func in tests.items():
                self._log(f"  测试 {name} 连接...")
                results[name] = await self._retry_async(test_func)
                status = "✅ 成功" if results[name].success else "❌ 失败"
                self._log(f"  {status} - {results[name].message}")
        
        return results
    
    async def run_task_tests(self) -> Dict[str, TestResult]:
        """运行任务测试"""
        tests = {
            '健康检查': self.test_health_check_task,
            '向量化': self.test_vectorize_task,
            '风险分析': self.test_analysis_task
        }
        
        results = {}
        
        if self.console:
            with Progress() as progress:
                task = progress.add_task("任务测试", total=len(tests))
                
                for name, test_func in tests.items():
                    progress.update(task, description=f"测试 {name} 任务...")
                    results[name] = await test_func()  # 任务测试不使用重试
                    progress.advance(task)
        else:
            self._log("⚙️ 开始任务测试...")
            for name, test_func in tests.items():
                self._log(f"📋 测试 {name} 任务...")
                results[name] = await test_func()
                status = "✅ 成功" if results[name].success else "❌ 失败"
                perf_info = f" (耗时: {results[name].duration:.2f}s)" if results[name].duration > 0 else ""
                self._log(f"  {status} - {results[name].message}{perf_info}")
        
        return results
    
    def display_results(self, connection_results: Dict[str, TestResult], task_results: Dict[str, TestResult]):
        """显示测试结果"""
        if self.console:
            self._display_results_rich(connection_results, task_results)
        else:
            self._display_results_simple(connection_results, task_results)
    
    def _display_results_rich(self, connection_results: Dict[str, TestResult], task_results: Dict[str, TestResult]):
        """使用Rich显示结果"""
        self._print_rich("\n" + "="*80)
        self._print_rich("[bold green]🎯 验证结果总结[/bold green]")
        self._print_rich("="*80)
        
        # 连接测试结果表格
        if connection_results:
            conn_table = Table(title="📡 连接测试结果", box=box.ROUNDED)
            conn_table.add_column("服务", style="cyan")
            conn_table.add_column("状态", justify="center")
            conn_table.add_column("详情", style="white")
            conn_table.add_column("耗时", style="yellow", justify="right")
            conn_table.add_column("错误代码", style="red")
            
            for service, result in connection_results.items():
                status = "✅ 成功" if result.success else "❌ 失败"
                duration = f"{result.duration:.2f}s"
                error_code = result.error_code or ""
                conn_table.add_row(service, status, result.message, duration, error_code)
            
            self._print_rich(conn_table)
        
        # 任务测试结果表格
        if task_results:
            task_table = Table(title="⚙️ 任务测试结果", box=box.ROUNDED)
            task_table.add_column("任务类型", style="cyan")
            task_table.add_column("状态", justify="center")
            task_table.add_column("结果", style="white")
            task_table.add_column("耗时", style="yellow", justify="right")
            task_table.add_column("详情", style="blue")
            
            for task_name, result in task_results.items():
                status = "✅ 成功" if result.success else "❌ 失败"
                duration = f"{result.duration:.2f}s"
                
                # 提取关键性能指标
                details_summary = ""
                if result.details:
                    if 'document_count' in result.details:
                        details_summary = f"文档: {result.details['document_count']}"
                    elif 'worker_healthy' in result.details:
                        details_summary = f"健康: {result.details['worker_healthy']}"
                    elif 'risk_level' in result.details:
                        details_summary = f"风险: {result.details['risk_level']}"
                
                task_table.add_row(task_name, status, result.message, duration, details_summary)
            
            self._print_rich(task_table)
        
        # 系统信息
        sys_info = self.get_system_info()
        if sys_info and HAS_PSUTIL:
            sys_table = Table(title="💻 系统资源状态", box=box.ROUNDED)
            sys_table.add_column("资源", style="cyan")
            sys_table.add_column("使用情况", style="white")
            
            if 'cpu_percent' in sys_info:
                sys_table.add_row("CPU", f"{sys_info['cpu_percent']:.1f}%")
            if 'memory_percent' in sys_info:
                sys_table.add_row("内存", f"{sys_info['memory_percent']:.1f}%")
            if 'disk_percent' in sys_info:
                sys_table.add_row("磁盘", f"{sys_info['disk_percent']:.1f}%")
            
            self._print_rich(sys_table)
    
    def _display_results_simple(self, connection_results: Dict[str, TestResult], task_results: Dict[str, TestResult]):
        """简单文本显示结果"""
        print("\n" + "="*80)
        print("🎯 验证结果总结")
        print("="*80)
        
        # 连接测试结果
        if connection_results:
            print("\n📡 连接测试结果:")
            print("-" * 60)
            for service, result in connection_results.items():
                status = "✅ 成功" if result.success else "❌ 失败"
                duration = f"({result.duration:.2f}s)"
                error_info = f" [{result.error_code}]" if result.error_code else ""
                print(f"  {service:12s} {status:8s} {result.message} {duration}{error_info}")
        
        # 任务测试结果
        if task_results:
            print("\n⚙️ 任务测试结果:")
            print("-" * 60)
            for task_name, result in task_results.items():
                status = "✅ 成功" if result.success else "❌ 失败"
                duration = f"({result.duration:.2f}s)"
                print(f"  {task_name:12s} {status:8s} {result.message} {duration}")
                
                # 显示详细信息
                if result.details:
                    for key, value in result.details.items():
                        if key in ['document_count', 'worker_healthy', 'risk_level', 'processing_time']:
                            print(f"    {key}: {value}")
        
        # 系统信息
        sys_info = self.get_system_info()
        if sys_info and HAS_PSUTIL:
            print("\n💻 系统资源状态:")
            print("-" * 40)
            if 'cpu_percent' in sys_info:
                print(f"  CPU使用率:     {sys_info['cpu_percent']:.1f}%")
            if 'memory_percent' in sys_info:
                print(f"  内存使用率:    {sys_info['memory_percent']:.1f}%")
            if 'disk_percent' in sys_info:
                print(f"  磁盘使用率:    {sys_info['disk_percent']:.1f}%")
    
    async def cleanup(self):
        """异步清理资源"""
        cleanup_tasks = []
        
        # 清理测试文件
        for file_path in self.test_files.copy():
            if file_path.exists():
                cleanup_tasks.append(asyncio.to_thread(file_path.unlink))
        
        # 关闭Redis连接
        if self._redis_client:
            cleanup_tasks.append(asyncio.to_thread(self._redis_client.close))
        
        # 执行所有清理任务
        if cleanup_tasks:
            try:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            except Exception as e:
                self._log(f"清理过程中出现错误: {e}", "warning")
        
        self.test_files.clear()
        self._log("资源清理完成")
    
    async def run_full_validation(self):
        """运行完整验证"""
        try:
            self.print_header()
            
            # 1. 连接测试
            self._print_rich("[bold blue]🔍 开始连接测试...[/bold blue]")
            connection_results = await self.run_connection_tests()
            self.results['connection_tests'] = connection_results
            
            # 检查关键连接
            redis_result = connection_results.get('Redis')
            celery_result = connection_results.get('Celery')
            
            if not redis_result or not redis_result.success:
                self._print_rich("[red]❌ Redis连接失败，无法继续任务测试[/red]")
                self.display_results(connection_results, {})
                return
            
            if not celery_result or not celery_result.success:
                self._print_rich("[yellow]⚠️ 未发现活跃Worker，部分任务测试可能失败[/yellow]")
            
            # 2. 任务测试
            self._print_rich("\n[bold blue]⚙️ 开始任务测试...[/bold blue]")
            task_results = await self.run_task_tests()
            self.results['task_tests'] = task_results
            
            # 3. 显示结果
            self.display_results(connection_results, task_results)
            
            # 4. 总结
            total_time = (datetime.now() - self.start_time).total_seconds()
            
            # 计算成功率
            conn_success = sum(1 for result in connection_results.values() if result.success)
            task_success = sum(1 for result in task_results.values() if result.success)
            
            total_tests = len(connection_results) + len(task_results)
            total_success = conn_success + task_success
            success_rate = (total_success / total_tests) * 100 if total_tests > 0 else 0
            
            # 计算平均性能
            avg_conn_time = sum(r.duration for r in connection_results.values()) / len(connection_results) if connection_results else 0
            avg_task_time = sum(r.duration for r in task_results.values()) / len(task_results) if task_results else 0
            
            self._print_rich(f"\n[bold]📊 验证完成 - 总耗时: {total_time:.2f}秒[/bold]")
            self._print_rich(f"[bold]📈 成功率: {success_rate:.1f}% ({total_success}/{total_tests})[/bold]")
            if connection_results:
                self._print_rich(f"[bold]⚡ 平均连接时间: {avg_conn_time:.2f}秒[/bold]")
            if task_results:
                self._print_rich(f"[bold]🏃 平均任务时间: {avg_task_time:.2f}秒[/bold]")
            
            # 状态评估
            if success_rate >= 80:
                self._print_rich("[bold green]🎉 Worker服务运行状况良好![/bold green]")
            elif success_rate >= 60:
                self._print_rich("[bold yellow]⚠️ Worker服务存在一些问题，建议检查[/bold yellow]")
            else:
                self._print_rich("[bold red]❌ Worker服务存在严重问题，需要立即处理[/bold red]")
            
            # 性能建议
            if avg_task_time > 30:
                self._print_rich("[yellow]💡 建议: 任务执行时间较长，考虑优化Worker配置或增加资源[/yellow]")
            
        finally:
            await self.cleanup()


def main():
    """主函数"""
    try:
        # 检查运行环境
        if not Path(project_root / "services" / "worker_service").exists():
            print("❌ 请在项目根目录运行此脚本")
            sys.exit(1)
        
        # 创建配置
        config = ValidationConfig()
        
        # 检查是否有Rich库用于增强体验
        if not HAS_RICH:
            print("ℹ️ 未安装Rich库，使用简化界面。建议运行: pip install rich")
        
        validator = OptimizedWorkerValidator(config)
        
        # 显示菜单
        if HAS_RICH:
            validator.console.print("[bold cyan]选择验证模式:[/bold cyan]")
            validator.console.print("1. 完整验证 (推荐)")
            validator.console.print("2. 仅连接测试")
            validator.console.print("3. 仅任务测试")
            validator.console.print("4. 退出")
            
            choice = Prompt.ask("请选择", choices=["1", "2", "3", "4"], default="1")
        else:
            print("选择验证模式:")
            print("1. 完整验证 (推荐)")
            print("2. 仅连接测试")
            print("3. 仅任务测试")
            print("4. 退出")
            
            choice = input("请选择 (1-4, 默认 1): ").strip() or "1"
        
        if choice == "4":
            print("👋 退出验证工具")
            return
        
        # 运行验证
        async def run_validation():
            if choice == "1":
                await validator.run_full_validation()
            elif choice == "2":
                validator.print_header()
                connection_results = await validator.run_connection_tests()
                validator.display_results(connection_results, {})
            elif choice == "3":
                validator.print_header()
                validator._print_rich("[yellow]⚠️ 跳过连接测试，直接运行任务测试[/yellow]")
                task_results = await validator.run_task_tests()
                validator.display_results({}, task_results)
        
        # 运行异步验证
        asyncio.run(run_validation())
        
    except KeyboardInterrupt:
        print("\n👋 用户中断，退出验证工具")
    except Exception as e:
        print(f"❌ 验证过程中发生错误: {e}")
        if HAS_PROJECT_MODULES and validator.config.verbose:
            traceback.print_exc()


if __name__ == "__main__":
    main() 