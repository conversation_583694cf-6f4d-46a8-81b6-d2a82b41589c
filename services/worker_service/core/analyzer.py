"""
风险分析器 (非流式模式)
基于原分析服务的实现，专门用于Celery异步任务
"""

import uuid
import time
import httpx
from typing import Dict, Any, Optional, List
from datetime import datetime

from shared.utils.logger import get_logger
from shared.models.analysis_result import AnalysisResult, AnalysisStatus
from services.worker_service.config import config
from services.worker_service.core.llm_client import LLMClient

logger = get_logger(__name__)


class RiskAnalyzer:
    """风险分析器 (非流式模式)"""
    
    def __init__(self):
        self.chroma_client = httpx.AsyncClient(
            base_url=config.chroma_url,
            timeout=30.0
        )
        self.llm_client = LLMClient()
        
        logger.info(f"风险分析器初始化完成 (非流式模式)，ChromaDB URL: {config.chroma_url}")
    
    def _get_collection_name(self, file_code: str) -> str:
        """获取集合名称"""
        return f"{config.chroma_collection_prefix}{file_code}"
    
    async def _retrieve_relevant_documents(
        self, file_code: str, query: str, n_results: int = 5
    ) -> List[Dict[str, Any]]:
        """
        检索相关文档
        """
        try:
            collection_name = self._get_collection_name(file_code)
            
            # 构建查询请求
            query_data = {
                "query_texts": [query],
                "n_results": n_results,
                "include": ["documents", "metadatas", "distances"]
            }
            
            response = await self.chroma_client.post(
                config.build_chroma_api_path(f"collections/{collection_name}/query"),
                json=query_data
            )
            
            if response.status_code != 200:
                logger.error(f"文档检索失败: HTTP {response.status_code}")
                return []
            
            result_data = response.json()
            
            # 解析查询结果
            documents = result_data.get("documents", [[]])[0]
            metadatas = result_data.get("metadatas", [[]])[0]
            distances = result_data.get("distances", [[]])[0]
            
            results = []
            for i in range(len(documents)):
                results.append({
                    "document": documents[i],
                    "metadata": metadatas[i] if i < len(metadatas) else {},
                    "distance": distances[i] if i < len(distances) else 0.0,
                })
            
            logger.debug(f"检索到 {len(results)} 个相关文档")
            return results
            
        except Exception as e:
            logger.error(f"文档检索失败: {e}")
            return []
    
    def _load_prompt_template(self, analysis_type: str = "default") -> str:
        """
        加载提示词模板
        """
        try:
            # 根据分析类型选择提示词文件
            prompt_file_map = {
                "default": "risk_analysis/default_analysis.md",
                "quick": "risk_analysis/quick_analysis.md",
                "detailed": "risk_analysis/detailed_analysis.md",
            }
            
            prompt_file = prompt_file_map.get(analysis_type, prompt_file_map["default"])
            prompt_path = f"{config.prompts_dir}/{prompt_file}"
            
            try:
                with open(prompt_path, 'r', encoding='utf-8') as f:
                    template = f.read()
                logger.debug(f"加载提示词模板: {prompt_file}")
                return template
            except FileNotFoundError:
                logger.warning(f"提示词文件不存在: {prompt_path}, 使用默认模板")
                return self._get_default_prompt_template()
                
        except Exception as e:
            logger.error(f"加载提示词模板失败: {e}")
            return self._get_default_prompt_template()
    
    def _get_default_prompt_template(self) -> str:
        """获取默认提示词模板"""
        return """
# 风险分析任务

你是一个专业的风险分析师，请基于以下数据进行风险评估。

## 分析数据
{context}

## 分析要求
1. 分析客户的违约概率
2. 识别主要风险因素
3. 提供风险等级评估
4. 给出具体的分析理由

## 输出格式
请以JSON格式返回分析结果：
```json
{{
    "default_probability": 0.0,
    "risk_level": "低风险|中风险|高风险",
    "risk_factors": ["因素1", "因素2"],
    "analysis_summary": "详细分析说明",
    "recommendations": ["建议1", "建议2"]
}}
```
"""
    
    def _format_context(self, documents: List[Dict[str, Any]]) -> str:
        """格式化上下文"""
        if not documents:
            return "没有找到相关数据"
        
        context_parts = []
        for i, doc in enumerate(documents, 1):
            context_parts.append(f"## 数据片段 {i}")
            context_parts.append(doc["document"])
            context_parts.append("")
        
        return "\n".join(context_parts)
    
    async def analyze_risk(
        self,
        file_code: str,
        analysis_type: str = "default",
        options: Optional[Dict[str, Any]] = None
    ) -> AnalysisResult:
        """
        执行风险分析 (非流式模式)
        """
        start_time = time.time()
        analysis_id = str(uuid.uuid4())
        
        try:
            logger.info(f"开始风险分析: {file_code}, 类型: {analysis_type}")
            
            # 1. 检索相关文档
            query = "风险评估 违约概率 客户数据"
            if options and "query" in options:
                query = options["query"]
            
            documents = await self._retrieve_relevant_documents(
                file_code, query, n_results=options.get("n_results", 5) if options else 5
            )
            
            if not documents:
                raise ValueError(f"未找到文件 {file_code} 的相关数据")
            
            # 2. 准备分析上下文
            context = self._format_context(documents)
            prompt_template = self._load_prompt_template(analysis_type)
            prompt = prompt_template.format(context=context)
            
            # 3. 调用LLM进行分析
            logger.info(f"调用LLM进行分析: {config.llm_provider}")
            llm_response = await self.llm_client.analyze(
                prompt=prompt,
                analysis_type=analysis_type,
                streaming=False  # 非流式模式
            )
            
            # 4. 解析分析结果
            analysis_result = self._parse_analysis_result(
                llm_response, analysis_id, file_code, start_time
            )
            
            logger.info(f"风险分析完成: {file_code}, 耗时: {analysis_result.processing_time:.2f}s")
            return analysis_result
            
        except Exception as e:
            logger.error(f"风险分析失败: {e}")
            
            # 返回失败结果
            return AnalysisResult(
                analysis_id=analysis_id,
                file_code=file_code,
                status=AnalysisStatus.FAILED,
                error_message=str(e),
                processing_time=time.time() - start_time,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
    
    def _parse_analysis_result(
        self, llm_response: str, analysis_id: str, file_code: str, start_time: float
    ) -> AnalysisResult:
        """解析LLM分析结果"""
        try:
            import json
            
            # 尝试从响应中提取JSON
            json_start = llm_response.find('{')
            json_end = llm_response.rfind('}') + 1
            
            if json_start >= 0 and json_end > json_start:
                json_str = llm_response[json_start:json_end]
                result_data = json.loads(json_str)
                
                return AnalysisResult(
                    analysis_id=analysis_id,
                    file_code=file_code,
                    status=AnalysisStatus.COMPLETED,
                    default_probability=result_data.get("default_probability", 0.0),
                    risk_level=result_data.get("risk_level", "未知"),
                    risk_factors=result_data.get("risk_factors", []),
                    analysis_summary=result_data.get("analysis_summary", ""),
                    recommendations=result_data.get("recommendations", []),
                    raw_response=llm_response,
                    processing_time=time.time() - start_time,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
            else:
                # 无法解析JSON，返回原始响应
                return AnalysisResult(
                    analysis_id=analysis_id,
                    file_code=file_code,
                    status=AnalysisStatus.COMPLETED,
                    analysis_summary=llm_response,
                    raw_response=llm_response,
                    processing_time=time.time() - start_time,
                    created_at=datetime.now(),
                    updated_at=datetime.now(),
                )
                
        except Exception as e:
            logger.error(f"解析分析结果失败: {e}")
            
            return AnalysisResult(
                analysis_id=analysis_id,
                file_code=file_code,
                status=AnalysisStatus.FAILED,
                error_message=f"结果解析失败: {str(e)}",
                raw_response=llm_response,
                processing_time=time.time() - start_time,
                created_at=datetime.now(),
                updated_at=datetime.now(),
            )
    
    async def health_check(self) -> Dict[str, bool]:
        """健康检查"""
        logger.info("开始风险分析器健康检查...")
        checks = {}
        
        # 检查ChromaDB连接
        logger.info("检查ChromaDB连接...")
        try:
            response = await self.chroma_client.get(config.build_chroma_api_path("heartbeat"))
            chroma_healthy = response.status_code == 200
            checks["chromadb"] = chroma_healthy
            if chroma_healthy:
                logger.info(f"✅ ChromaDB连接正常: {config.chroma_url}")
            else:
                logger.warning(f"❌ ChromaDB连接失败: HTTP {response.status_code}")
        except Exception as e:
            logger.error(f"❌ ChromaDB连接异常: {e}")
            checks["chromadb"] = False
        
        # 检查LLM服务
        logger.info("检查LLM服务...")
        try:
            llm_healthy = await self.llm_client.health_check()
            # 确保返回值是布尔类型
            llm_healthy = bool(llm_healthy) if llm_healthy is not None else False
            checks["llm_service"] = llm_healthy
            logger.info(f"LLM健康检查原始结果: {repr(llm_healthy)}, 类型: {type(llm_healthy)}")
            if llm_healthy:
                logger.info("✅ LLM服务正常")
            else:
                logger.warning("❌ LLM服务不健康")
        except Exception as e:
            logger.error(f"❌ LLM服务检查异常: {e}")
            checks["llm_service"] = False
        
        logger.info(f"风险分析器健康检查完成: {checks}")
        return checks
    
    async def cleanup(self):
        """清理资源"""
        await self.chroma_client.aclose()
        await self.llm_client.cleanup()
